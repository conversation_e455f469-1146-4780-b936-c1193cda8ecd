<template>
  <div class="bg-bg-container p-4">
    <a-form
      :model="formState"
      class="p-4"
    >
      <a-row :gutter="[16, 8]">
        <!-- 舆情信息 -->
        <a-col :span="24"><span class="text-base font-bold">舆情信息</span></a-col>

        <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
          <a-form-item label="舆情摘要">
            <a-input v-model:value="formState.summary" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
          <a-form-item label="原文内容">
            <a-input v-model:value="formState.content" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
          <a-form-item label="舆情类别">
            <div class="flex gap-1">
              <a-select
                v-model:value="formState.mainCategory"
                class="flex-1"
                :options="primaryTypeOption"
                @change="categoryChange"
              />
              <a-select
                v-model:value="formState.category"
                class="flex-1"
                :options="secondTypeOption"
                @change="secondTypeChange"
              />
            </div>
          </a-form-item>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
          <a-form-item label="重大舆情">
            <c-boolean-select v-model:value="formState.serious" allow-clear />
          </a-form-item>
        </a-col>

        <!-- 以下内容默认收起，点击“展开”再显示 -->
        <template v-if="isExpanded">
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="上报教育厅">
              <c-boolean-select v-model:value="formState.isJyt" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="每日简报">
              <c-enum-select
                v-model:value="formState.toDayBriefing"
                :enum="ToDayBriefing"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="正面报道">
              <c-boolean-select v-model:value="formState.positive" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="隐患舆情">
              <c-boolean-select v-model:value="formState.isHiddenDanger" allow-clear />
            </a-form-item>
          </a-col>

          <!-- 发帖信息 -->
          <a-col :span="24"><span class="text-base font-bold">发帖信息</span></a-col>

          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="发帖人">
              <a-input v-model:value="formState.publisher" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="发帖渠道">
              <c-select
                v-model:value="formState.source"
                :field-names="{ label: 'name', value: 'name' }"
                :api="api.WebSources.List_GetAsync"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="发布时间">
              <c-range-picker v-model:start-time="formState.publisherStart" v-model:end-time="formState.publisherEnd" :show-time="{ format: 'HH:mm' }" style="width: 100%;" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="录入时间">
              <c-range-picker v-model:start-time="formState.createdStart" v-model:end-time="formState.createdEnd" :show-time="{ format: 'HH:mm' }" style="width: 100%;" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="录入人">
              <a-input v-model:value="formState.poster" />
            </a-form-item>
          </a-col>

          <!-- 涉及地区单位 -->
          <a-col :span="24"><span class="text-base font-bold">涉及地区单位</span></a-col>

          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="涉及单位">
              <a-input v-model:value="formState.department" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="单位类型">
              <c-standard
                v-model:value="formState.departmentType" el="select" s-key="dept-type" show-search
                :field-names="{ label: 'label', value: 'value' }"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
            <a-form-item label="涉及地区">
              <c-standard
                v-model:value="formState.counties" el="select" s-key="county" show-search
                :field-names="{ label: 'label', value: 'value', options: 'children' }" placeholder="选择事件发生地点"
              />
            </a-form-item>
          </a-col>

          <!-- 分类标签 -->
          <template v-if="formState.mainCategory && formState.category">
            <a-col :span="24"><span class="text-base font-bold">分类标签</span></a-col>

            <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
              <a-form-item label="事故类型（教育厅）">
                <a-select
                  v-model:value="formState.accidentType"
                  class="flex-1"
                  :options="accidentTypeOption"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
              <a-form-item label="事故原因">
                <a-select
                  v-model:value="formState.accidentReason"
                  class="flex-1"
                  :options="accidentReasonOption"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
              <a-form-item label="事发地点">
                <a-select
                  v-model:value="formState.address"
                  class="flex-1"
                  :options="adressOption"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
              <a-form-item label="隐患类别">
                <a-select
                  v-model:value="formState.trafficCategory"
                  class="flex-1"
                  :options="trafficCategoryOption"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
              <a-form-item label="涉及人数">
                <a-input-number v-model:value="formState.involved" :min="0" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
              <a-form-item label="死亡人数">
                <a-input-number v-model:value="formState.death" :min="0" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :lg="8" :xxl="6">
              <a-form-item label="受伤人数">
                <a-input-number v-model:value="formState.injured" :min="0" style="width: 100%" />
              </a-form-item>
            </a-col>
          </template>
        </template>

        <!-- 操作按钮 -->
        <a-col :span="24" class="mt-2 flex items-center justify-end gap-4 text-right">
          <a-button type="primary" :icon="h(SearchOutlined)" @click="onSearch">查询</a-button>
          <a-button class="ml-4" :icon="h(ReloadOutlined)" @click="onReset">重置</a-button>
          <a-button type="link" :icon="isExpanded ? h(UpOutlined) : h(DownOutlined)" @click="isExpanded = !isExpanded">
            {{ isExpanded ? '收起' : '展开' }}
          </a-button>
        </a-col>
      </a-row>
    </a-form>
  </div>
  <div>
    <c-pro-table
      ref="proTableRef" :row-class-name="(_record, index) => (index % 2 === 1 ? 'c2-table-striped' : 'c2-table-prototype')"
      :row-key="(record) => record.id" :row-selection="rowSelection" size="small"
      :columns="columns"
      :api="api.OpinionManage.GetListAsync"
      :show-search="false"
      :show-tool-btn="false" immediate operation serial-number :get-params="formState"
      @after-fetch="afterFetch"
    >
      <template #header>
        <div class="w-full flex justify-between">
          <div class="flex items-center c-text">
            <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
            <div class="ml-4 text-16px font-bold">
              舆情信息（{{ totals }}）
              <a-button v-if="$auth(_Role.舆情监测人员)" type="primary" :icon="h(PlusOutlined)" @click="onAdd">
                录入
              </a-button>
            </div>
          </div>
          <div class="space-x-4">
            <a-button type="primary" :disabled="rowSelection.selectedRowKeys.length <= 0">
              加入AI分析
            </a-button>
            <a-button type="primary" @click="useExport.exportDailyNewspaper">
              日报格式导出
            </a-button>
            <a-button type="primary">
              月报格式导出
            </a-button>
            <a-button type="primary" @click="useExport.exportBriefing">
              简报格式导出
            </a-button>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'summary'">
          <div class="flex space-x-2">
            <div v-if="record.dept?.isCooperate" class="w-42px rounded-md bg-#F0B800 text-center text-3 c-#fff">合作</div>
            <div v-if="record.topicId" class="w-42px rounded-md bg-#7948EA text-center text-3 c-#fff">专题</div>
            <div v-if="record.serious" class="w-42px rounded-md bg-#FF5252 text-center text-3 c-#fff">重大</div>
            <div v-if="record.eventId" class="w-42px rounded-md bg-#2A82E4 text-center text-3 c-#fff">事件</div>
          </div>
          <a @click="onEdit(record, true)">
            <c-truncated-text :text="record.summary" :max-lines="2" />
          </a>
        </template>
        <template v-if="column.dataIndex === 'content'">
          <c-truncated-text :text="record.content" :max-lines="2" />
          <div class="flex space-x-2">
            <div v-for="(img, idx) in record.images" :key="idx" class="coverBox size-30px overflow-hidden">
              <c-image
                :src="joinFilePathById(img)" alt="avatar" :preview="true"
                style="height: 30px; width:30px ; object-fit:cover"
              />
            </div>
          </div>
          <div>
            <a v-for="(url, idx) in record.url?.split(/\r?\n/)" :key="idx" :href="url" target="_blank"> {{ url }} </a>
          </div>
        </template>
        <template v-if="column.dataIndex === 'publisher'">
          <div class="flex items-center">
            <c-image
              :src="joinFilePathById(getByName(record.source)?.iconId!)" alt="avatar" :preview="true"
              style="height: 20px; width:20px ; object-fit:cover"
            />
            <span class="ml-1">{{ record.publisher }}</span>
          </div>
          <div>id: {{ record.sourceId }}</div>
          <div class="c-text-secondary">{{ dateTime(record.published, 'YYYY-MM-DD HH:mm') }}</div>
        </template>
        <template v-if="column.dataIndex === 'createdUser'">
          <div>{{ record.createdUser?.userName }}</div>
          <span class="c-text-secondary">{{ dateTime(record.createdAt, 'YYYY-MM-DD HH:mm') }}</span>
        </template>
        <template v-if="column.dataIndex === 'dept'">
          <div class="text-center">
            <div>{{ record.dept?.name }}</div>
            <span>{{ record.address }}</span>
            <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导]) && record.dept?.isCooperate">
              <a @click="onReminder(record)">
                <c-icon-comment-outlined class="mr-1" :class="{ 'c-#58B869': record.isPushDept }" />{{ record.isPushDept ? '再次提醒' : '提醒' }}</a>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'auditStatus'">
          <div class="text-center">
            <a-tag class="mr-0!" :color="auditTypeColor(record.auditStatus)">
              {{ opinionTypeText(record.auditStatus) }}
            </a-tag>
            <div class="mt-1">
              <a v-if="$auth(_Role.舆情监测中心领导) && (record.auditStatus === AuditType.未读 || record.auditStatus === AuditType.需要处置)" @click="onAudit(record, AuditType.处置)">确认上报</a>
              <a v-if="$auth(_Role.舆情监测中心领导) && record.auditStatus === AuditType.处置" @click="onAudit(record, AuditType.处置)">重新上报</a>
              <a v-if="$auth(_Role.舆情监测人员) && record.auditStatus === AuditType.未读" @click="onAudit(record, AuditType.需要处置)">上报</a>
            </div>
          </div>
        </template>
      </template>
      <template #operation="{ record }">
        <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])">
          <a @click="onEdit(record, false)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm
            title="确认删除此舆情吗?"
            ok-text="确认"
            cancel-text="取消"
            @confirm="onDel(record.id)"
          >
            <span class="cursor-pointer c-error">删除</span>
          </a-popconfirm>
        </div>
        <div>
          <a @click="onCopy(record.id)">简报复制</a>
        </div>
        <div>
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              更多操作
              <c-icon-down-outlined />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="onTopicAdd(record)">
                  <a href="javascript:;">添加到专题</a>
                </a-menu-item>
                <a-menu-item @click="onAddEvent(record)">
                  <a href="javascript:;">添加到事件</a>
                </a-menu-item>
                <a-menu-item @click="onRepostEvent(record)">
                  <a href="javascript:;">转为事件</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </c-pro-table>
  </div>

  <EditForm v-model:open="open" v-model="currentObj" :read-only="readOnly" @save="proTableRef.search()" />

  <!-- 舆情添加到事件 -->
  <a-drawer v-model:open="addEventOpen" title="舆情添加到事件" destroy-on-close width="860px" @close="onClose">
    <a-form
      ref="eventFormRef" :model="addEventForm" name="basic" autocomplete="off"
      :label-col="{ style: { width: '82px' } }" @finish="onEventFinish"
    >
      <a-form-item label="事件" name="eventId" :rules="[{ required: true, message: '请选择事件!' }]">
        <c-select
          v-model:value="addEventForm.eventId" selected-first
          :field-names="{ label: 'name', value: 'id', options: 'children' }" :api="api.EventManage.GetListAsync"
        />
      </a-form-item>
      <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
        <a-button type="primary" html-type="submit">保存</a-button>
      </a-form-item>
    </a-form>
  </a-drawer>

  <!-- 合作单位推送 -->
  <PushReminder v-model:open="pushReminderOpen" :current-data="currentObj" />

  <!-- 舆情添加到专题 -->
  <a-drawer v-model:open="topicOpen" title="舆情添加到专题" destroy-on-close width="860px" @close="topicOpen = false">
    <a-form :model="topicValue" autocomplete="off" :label-col="{ style: { width: '82px' } }" @finish="onTopicFinish">
      <a-form-item label="专题" name="topicId" :rules="[{ required: true, message: '请选择专题!' }]">
        <c-select
          v-model:value="topicValue.topicId" selected-first :field-names="{ label: 'title', value: 'id' }"
          :api="api.OpinionTopicManage.GetListAsync"
        />
      </a-form-item>
      <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
        <a-button type="primary" html-type="submit">保存</a-button>
      </a-form-item>
    </a-form>
  </a-drawer>

  <!-- 舆情转事件 -->
  <a-drawer v-model:open="eventEditOpen" title="转为事件" destroy-on-close width="860px" @close="eventEditClose">
    <template #extra>
      <a-button style="margin-right: 8px" @click="eventEditClose">取消</a-button>
      <a-button type="primary" @click="eventEditSave">保存</a-button>
    </template>
    <CreateForm ref="eventEditFormRef" v-model="currentEventObj" />
  </a-drawer>

  <!-- 提交审核 -->
  <AuditModal v-model:open="auditOpen" v-model:jyt-opinion-id="jytOpinionId" :params="auditParams" title="确认提交到舆情审核员审核吗？" @submit="proTableRef?.search" />
</template>

<script setup lang="ts">
import type { PublicOpinionViewModel, TagManageView } from '@/api/models'
import type { SelectProps } from 'ant-design-vue'
import * as api from '@/api'
import { AuditType, PublicEventCreateModel, PublicOpinionEditModel, TagType, ToDayBriefing } from '@/api/models'

import { useSocialMediaCache } from '@/hooks/useSocialMediaCache'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import CreateForm from '@/views/event-library/components/CreateForm.vue'
import { DownOutlined, PlusOutlined, ReloadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import AuditModal from './components/AuditModal.vue'
import EditForm from './components/EditForm.vue'
import PushReminder from './components/PushReminder.vue'
import { useOperateHook } from './useOperateHook'

definePage({
  meta: {
    title: '舆情库',
    icon: 'ExceptionOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '舆情库',
        local: true,
        icon: 'ExceptionOutlined',
        order: 7,
      },
    },
  },
})

const { getByName } = useSocialMediaCache()

const useExport = useExportHook()

const { open, readOnly, currentObj, onAdd, onEdit } = useEditHook()

const { pushReminderOpen, onReminder } = useReminderHook()

const { eventEditOpen, eventEditFormRef, currentEventObj, eventEditClose, eventEditSave, onRepostEvent } = useEventEditHook()

const { eventFormRef, addEventOpen, addEventForm, onEventFinish, onClose, onAddEvent } = useEventHook()

const { topicOpen, topicValue, onTopicFinish, onTopicAdd } = useTopicHook()

const { auditOpen, jytOpinionId, auditParams, onAudit } = useAuditHook()

const { primaryTypeOption, secondTypeOption, accidentTypeOption, accidentReasonOption, trafficCategoryOption, adressOption, categoryChange, getTypeData, secondTypeChange } = useTypeHook()

const isExpanded = ref(false)

const formState = ref<Parameters<typeof api.OpinionManage.GetListAsync>[0]>({
  createdStart: dayjs().subtract(1, 'day').hour(12).minute(0).second(0).millisecond(0),
  createdEnd: dayjs(),
})

const proTableRef = useTemplateRef('proTableRef')

const totals = ref(0)

const columns = ref([
  {
    title: '舆情摘要',
    dataIndex: 'summary',
  },
  { title: '发帖人信息', dataIndex: 'publisher', width: 240 },
  { title: '原文内容', dataIndex: 'content' },
  { title: '舆情类别', dataIndex: 'category', width: 120 },
  { title: '涉及地区/单位', dataIndex: 'dept', width: 150 },
  { title: '录入人', dataIndex: 'createdUser', width: 160 },
  { title: '上报状态', dataIndex: 'auditStatus', width: 120, align: 'center' },
])

const rowSelection = ref({
  selectedRowKeys: [] as string[],
  onChange: (selectedRowKeys: string[]) => {
    rowSelection.value.selectedRowKeys = selectedRowKeys
  },
})

function onSearch() {
  proTableRef.value?.search()
}

function onReset() {
  formState.value = {
    createdStart: dayjs().subtract(1, 'day').hour(12).minute(0).second(0).millisecond(0),
    createdEnd: dayjs().hour(12).minute(0).second(0).millisecond(0),
  }
  proTableRef.value?.search()
}

function afterFetch(e: { totals: number }) {
  totals.value = e.totals
}

const { onDel, onCopy } = useOperateHook(onSearch)

function useAuditHook() {
  const auditOpen = ref(false)

  const jytOpinionId = ref(Guid.empty)

  const auditParams = ref({
    id: Guid.empty,
    audit: AuditType.需要处置,
  })

  function onAudit(record: PublicOpinionViewModel, audit: AuditType) {
    auditParams.value = { id: record.id, audit }
    jytOpinionId.value = record.jytEntityId
    auditOpen.value = true
  }

  return { auditOpen, jytOpinionId, auditParams, onAudit }
}

function useEditHook() {
  const open = ref(false)

  const readOnly = ref(false)

  const currentObj = ref(new PublicOpinionEditModel())

  function onAdd() {
    readOnly.value = false
    currentObj.value = new PublicOpinionEditModel()
    open.value = true
  }

  function onEdit(record: PublicOpinionViewModel, isRead: boolean) {
    currentObj.value = viewModelToEditModel(record, new PublicOpinionEditModel())
    readOnly.value = isRead
    open.value = true
  }

  return { open, readOnly, currentObj, onAdd, onEdit }
}

function useReminderHook() {
  const pushReminderOpen = ref(false)

  function onReminder(record: PublicOpinionViewModel) {
    currentObj.value = record

    pushReminderOpen.value = true
  }

  return { pushReminderOpen, onReminder }
}

/** 舆情类别 */
function useTypeHook() {
  const typeOption = ref<TagManageView[]>([])

  const secondTypeData = ref<TagManageView[]>([])

  const primaryTypeOption = ref<SelectProps['options']>([])

  const secondTypeOption = ref<SelectProps['options']>([])

  const accidentTypeOption = ref<SelectProps['options']>([]) // 事故类型

  const accidentReasonOption = ref<SelectProps['options']>([]) // 事故原因

  const trafficCategoryOption = ref<SelectProps['options']>([]) // 隐患类别

  const adressOption = ref<SelectProps['options']>([]) // 事发地点

  function categoryChange() {
    formState.value.category = ''

    secondTypeData.value = typeOption.value.find(item => item.value === formState.value.mainCategory)?.children || []

    secondTypeOption.value = typeOption.value.find(item => item.value === formState.value.mainCategory)
      ?.children
      ?.map(item => ({ label: item.value, value: item.value,
      })) || []
  }

  function secondTypeChange() {
    const temp = secondTypeData.value.find(item => item.value === formState.value.category)?.children || []

    accidentTypeOption.value = temp.filter(item => item.tagType === TagType.事故类型)?.map(item => ({ label: item.value, value: item.value }))
    accidentReasonOption.value = temp.filter(item => item.tagType === TagType.事故原因)?.map(item => ({ label: item.value, value: item.value }))
    trafficCategoryOption.value = temp.filter(item => item.tagType === TagType.隐患类别)?.map(item => ({ label: item.value, value: item.value }))
    adressOption.value = temp.filter(item => item.tagType === TagType.事发地点)?.map(item => ({ label: item.value, value: item.value }))
  }

  async function getTypeData() {
    typeOption.value = await api.TagManages.GetTagTreeAsync({})
    primaryTypeOption.value = typeOption.value.filter(item => item.tagType === TagType.一级分类)?.map(item => ({ label: item.value, value: item.value }))
  }

  return { typeOption, primaryTypeOption, secondTypeOption, accidentTypeOption, accidentReasonOption, trafficCategoryOption, adressOption, categoryChange, getTypeData, secondTypeChange }
}

/** 添加到事件 */
function useEventHook() {
  const eventFormRef = useTemplateRef('eventFormRef')

  const addEventOpen = ref(false)

  const addEventForm = ref({
    opinionId: Guid.empty,
    eventId: Guid.empty,
    forcibly: false,
  })

  async function onEventFinish() {
    try {
      await api.OpinionManage.AddOpinion2Event_PostAsync(addEventForm.value)
      addEventOpen.value = false
      proTableRef.value?.search()

      message.success('保存成功')
    }
    catch (error: any) {
      message.error(`保存失败：${error.message}`)
    }
  }

  function onAddEvent(record: PublicOpinionViewModel) {
    addEventForm.value.opinionId = record.id
    addEventOpen.value = true
  }

  function onClose() {
    currentObj.value = new PublicOpinionEditModel()
    open.value = false
  }

  return { eventFormRef, addEventOpen, addEventForm, onEventFinish, onAddEvent, onClose }
}

const router = useRouter()

/** 舆情转事件 */
function useEventEditHook() {
  const eventEditOpen = ref(false)

  const currentEventObj = ref(new PublicEventCreateModel())

  const eventEditFormRef = useTemplateRef('eventEditFormRef')

  function eventEditClose() { }

  function onRepostEvent(record: PublicOpinionViewModel) {
    currentEventObj.value.title = record.summary
    currentEventObj.value.publicOpinionIds = [record.id as string]
    eventEditOpen.value = true
  }

  async function eventEditSave() {
    const data = await eventEditFormRef.value?.onSubmit()
    proTableRef.value?.search()
    eventEditOpen.value = false
    message.success('保存成功')
    router.push({ path: '/event-library/detail', query: { id: data.id } })
  }

  return { eventEditOpen, eventEditFormRef, currentEventObj, eventEditClose, eventEditSave, onRepostEvent }
}

/** 添加到专题 */
function useTopicHook() {
  const topicValue = ref({
    opinionId: '',
    topicId: '',
    forcibly: false,
  })

  const topicOpen = ref(false)

  async function onTopicFinish() {
    try {
      await api.OpinionTopicManage.AddOpinion2Topic_PostAsync(topicValue.value)
      proTableRef.value?.search()
      topicOpen.value = false
      message.success('添加到专题成功')
    }
    catch (error: any) {
      message.error(`失败：${error.message}`)
    }
  }

  function onTopicAdd(record: PublicOpinionViewModel) {
    topicValue.value.opinionId = record.id!
    topicOpen.value = true
  }

  return {
    topicValue,
    topicOpen,
    onTopicFinish,
    onTopicAdd,
  }
}

/** 导出 */
function useExportHook() {
  function exportDailyNewspaper() {
    useDownload(() => api.OpinionManage.ExportDailyNewspaperWord_PostAsync({ }))
  }

  function exportBriefing() {
    useDownload(() => api.OpinionManage.ExportSimpleDailyReport_GetAsync({ }))
  }

  return { exportDailyNewspaper, exportBriefing }
}

onMounted(() => {
  getTypeData()
})
</script>

<style scoped lang="less">
:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}

:deep(.header-button-lf) {
  width: 100%;
}
</style>
